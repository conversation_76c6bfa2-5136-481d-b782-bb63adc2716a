<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title>B<PERSON>vani Doors Admin</mat-card-title>
      <mat-card-subtitle>Please sign in to continue</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form (submit)="login()" class="login-form">
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Username</mat-label>
          <input matInput
                 [value]="username()"
                 (input)="username.set($any($event.target).value)"
                 name="username"
                 required>
        </mat-form-field>

        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput
                 type="password"
                 [value]="password()"
                 (input)="password.set($any($event.target).value)"
                 name="password"
                 required>
        </mat-form-field>

        <button mat-raised-button
                color="primary"
                type="submit"
                class="login-button full-width"
                [disabled]="isLoading()">
          <span>Sign In</span>
        </button>

        <div *ngIf="error()" class="error">
          {{ error() }}
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
