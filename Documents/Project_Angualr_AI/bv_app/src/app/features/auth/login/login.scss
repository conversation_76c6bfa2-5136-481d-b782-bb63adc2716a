.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 24px;

  @media (max-width: 480px) {
    padding: 16px;
    margin: 0;
  }
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.full-width {
  width: 100%;
}

.login-button {
  height: 48px;
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f44336;
  font-size: 14px;
  margin-top: 8px;

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

mat-card-header {
  text-align: center;
  margin-bottom: 16px;

  mat-card-title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }

  mat-card-subtitle {
    font-size: 14px;
    color: #666;
    margin-top: 8px;
  }
}