.dashboard-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// Responsive padding using BreakpointObserver classes
.responsive-tablet .dashboard-container,
.responsive-desktop .dashboard-container {
  padding: 24px;
}

.dashboard-header {
  background: white;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .header-content {
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    h1 {
      margin: 0;
      color: #333;
      font-size: 24px;
      font-weight: 500;
    }
  }
}

// Responsive header styles using BreakpointObserver classes
.responsive-mobile .dashboard-header .header-content h1 {
  font-size: 20px;
}

.responsive-mobile .dashboard-header .header-content .user-info {
  display: flex;
  align-items: center;
  gap: 12px;

  span {
    color: #666;
    font-size: 14px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;

  p {
    margin-top: 16px;
    color: #666;
  }
}

.stats-grid {
  margin-bottom: 32px;

  .stat-card {
    height: 100%;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    &.alert {
      border-left: 4px solid #ff9800;
    }

    &.warning {
      border-left: 4px solid #f44336;
    }

    mat-card-content {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 16px !important;
    }
  }

  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 100%;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;
      color: white;
    }

    &.materials { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    &.suppliers { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    &.customers { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    &.orders { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    &.stock-value { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    &.profit { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
    &.pending { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
    &.low-stock { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
  }

  .stat-info {
    flex: 1;

    h3 {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.quick-actions {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
    font-weight: 500;
  }

  .actions-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    button {
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Additional responsive styles using BreakpointObserver classes
.responsive-mobile {
  .stat-info h3 {
    font-size: 20px;
  }

  .quick-actions .actions-grid {
    grid-template-columns: 1fr;
  }
}