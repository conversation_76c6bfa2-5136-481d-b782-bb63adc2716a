<app-responsive-container [class]="containerClasses()" [isCard]="false" [fullHeight]="true">
  <!-- Header -->
  <app-responsive-container [isCard]="true" layout="flex-row" alignment="space-between">
    <h1><PERSON><PERSON>vani Doors Admin Dashboard</h1>
    @if (user()) {
      <div class="user-info">
        <span>Welcome, {{ user()?.first_name || user()?.username }}!</span>
        <button mat-icon-button (click)="logout()" title="Logout">
          <mat-icon>logout</mat-icon>
        </button>
      </div>
    }
  </app-responsive-container>

  <!-- Welcome Card -->
  @if (user()) {
    <mat-card>
      <h2>Welcome, {{ user()?.username }}</h2>
      <p>This is your dashboard.</p>
    </mat-card>
  }
</app-responsive-container>
