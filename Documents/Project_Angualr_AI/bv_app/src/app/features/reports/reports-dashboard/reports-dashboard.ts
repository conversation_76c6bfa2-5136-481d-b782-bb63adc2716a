import { Component, signal, computed, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule } from '@angular/forms';
import { DashboardService, DashboardStats } from '../../../core/services/dashboard';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { ResponsiveContainerComponent } from '../../../shared/components/responsive-container/responsive-container.component';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-reports-dashboard',
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
    FormsModule,
    ResponsiveContainerComponent,
    LoadingComponent
  ],
  templateUrl: './reports-dashboard.html',
  styleUrl: './reports-dashboard.scss'
})
export class ReportsDashboard implements OnInit {
  private dashboardService = inject(DashboardService);
  private responsiveService = inject(ResponsiveService);

  // Signals for state management
  private statsSignal = signal<DashboardStats | null>(null);
  private loadingSignal = signal<boolean>(true);
  private startDateSignal = signal<Date>(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  private endDateSignal = signal<Date>(new Date());

  // Public readonly signals
  readonly stats = this.statsSignal.asReadonly();
  readonly isLoading = this.loadingSignal.asReadonly();
  readonly startDate = this.startDateSignal.asReadonly();
  readonly endDate = this.endDateSignal.asReadonly();

  // Responsive signals
  readonly isMobile = computed(() => this.responsiveService.isMobile());
  readonly containerClasses = computed(() => this.responsiveService.containerClasses());
  readonly cols = computed(() => this.responsiveService.gridColumns());

  // Report cards configuration
  readonly reportCards = [
    {
      title: 'Sales Report',
      description: 'View detailed sales analytics and trends',
      icon: 'trending_up',
      color: 'primary',
      action: () => this.generateSalesReport()
    },
    {
      title: 'Inventory Report',
      description: 'Check stock levels and inventory status',
      icon: 'inventory_2',
      color: 'accent',
      action: () => this.generateInventoryReport()
    },
    {
      title: 'Customer Report',
      description: 'Analyze customer behavior and orders',
      icon: 'people',
      color: 'primary',
      action: () => this.generateCustomerReport()
    },
    {
      title: 'Supplier Report',
      description: 'Review supplier performance and orders',
      icon: 'local_shipping',
      color: 'accent',
      action: () => this.generateSupplierReport()
    },
    {
      title: 'Financial Report',
      description: 'View profit, loss, and financial metrics',
      icon: 'account_balance',
      color: 'primary',
      action: () => this.generateFinancialReport()
    },
    {
      title: 'GST Report',
      description: 'Generate GST returns and tax reports',
      icon: 'receipt',
      color: 'accent',
      action: () => this.generateGstReport()
    }
  ];

  ngOnInit(): void {
    this.loadStats();
  }

  loadStats(): void {
    this.loadingSignal.set(true);
    this.dashboardService.getDashboardStats().subscribe({
      next: (stats) => {
        this.statsSignal.set(stats);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Error loading stats:', error);
        this.loadingSignal.set(false);
      }
    });
  }

  onStartDateChange(date: Date): void {
    this.startDateSignal.set(date);
  }

  onEndDateChange(date: Date): void {
    this.endDateSignal.set(date);
  }

  generateSalesReport(): void {
    console.log('Generating sales report...');
    // TODO: Implement sales report generation
  }

  generateInventoryReport(): void {
    console.log('Generating inventory report...');
    // TODO: Implement inventory report generation
  }

  generateCustomerReport(): void {
    console.log('Generating customer report...');
    // TODO: Implement customer report generation
  }

  generateSupplierReport(): void {
    console.log('Generating supplier report...');
    // TODO: Implement supplier report generation
  }

  generateFinancialReport(): void {
    console.log('Generating financial report...');
    // TODO: Implement financial report generation
  }

  generateGstReport(): void {
    console.log('Generating GST report...');
    // TODO: Implement GST report generation
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }
}
