<app-responsive-container [class]="containerClasses()" [isCard]="false" [fullHeight]="true">
  <!-- Header -->
  <app-responsive-container [isCard]="true" layout="flex-row" alignment="space-between">
    <h1>Reports & Analytics</h1>
    <div class="date-filters">
      <mat-form-field appearance="outline">
        <mat-label>Start Date</mat-label>
        <input matInput [matDatepicker]="startPicker" [value]="startDate()" (dateChange)="onStartDateChange($event.value)">
        <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
        <mat-datepicker #startPicker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Date</mat-label>
        <input matInput [matDatepicker]="endPicker" [value]="endDate()" (dateChange)="onEndDateChange($event.value)">
        <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
        <mat-datepicker #endPicker></mat-datepicker>
      </mat-form-field>
    </div>
  </app-responsive-container>

  <!-- Loading State -->
  @if (isLoading()) {
    <app-loading
      message="Loading reports data..."
      [compact]="isMobile()"
      [fullHeight]="false">
    </app-loading>
  } @else {
    <!-- Quick Stats Overview -->
    @if (stats()) {
      <app-responsive-container [isCard]="true">
        <h2>Quick Overview</h2>
        <div class="stats-grid">
          <mat-grid-list [cols]="cols()" rowHeight="120px" gutterSize="16px">
            <mat-grid-tile>
              <mat-card class="stat-card">
                <mat-card-content>
                  <div class="stat-content">
                    <div class="stat-icon materials">
                      <mat-icon>inventory_2</mat-icon>
                    </div>
                    <div class="stat-info">
                      <h3>{{ stats()!.materials.total }}</h3>
                      <p>Total Materials</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile>

            <mat-grid-tile>
              <mat-card class="stat-card">
                <mat-card-content>
                  <div class="stat-content">
                    <div class="stat-icon customers">
                      <mat-icon>people</mat-icon>
                    </div>
                    <div class="stat-info">
                      <h3>{{ stats()!.customers.total }}</h3>
                      <p>Total Customers</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile>

            <mat-grid-tile>
              <mat-card class="stat-card">
                <mat-card-content>
                  <div class="stat-content">
                    <div class="stat-icon financial">
                      <mat-icon>account_balance_wallet</mat-icon>
                    </div>
                    <div class="stat-info">
                      <h3>{{ formatCurrency(stats()!.financial.total_order_value) }}</h3>
                      <p>Total Revenue</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile>

            <mat-grid-tile>
              <mat-card class="stat-card">
                <mat-card-content>
                  <div class="stat-content">
                    <div class="stat-icon stock">
                      <mat-icon>trending_up</mat-icon>
                    </div>
                    <div class="stat-info">
                      <h3>{{ formatCurrency(stats()!.financial.total_stock_value) }}</h3>
                      <p>Stock Value</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile>
          </mat-grid-list>
        </div>
      </app-responsive-container>
    }

    <!-- Report Cards -->
    <app-responsive-container [isCard]="true">
      <h2>Available Reports</h2>
      <div class="reports-grid">
        <mat-grid-list [cols]="cols()" rowHeight="200px" gutterSize="16px">
          @for (report of reportCards; track report.title) {
            <mat-grid-tile>
              <mat-card class="report-card" (click)="report.action()">
                <mat-card-header>
                  <div class="report-icon" [class]="report.color">
                    <mat-icon>{{ report.icon }}</mat-icon>
                  </div>
                  <mat-card-title>{{ report.title }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ report.description }}</p>
                </mat-card-content>
                <mat-card-actions>
                  <button mat-raised-button [color]="report.color" (click)="report.action()">
                    <mat-icon>file_download</mat-icon>
                    Generate
                  </button>
                </mat-card-actions>
              </mat-card>
            </mat-grid-tile>
          }
        </mat-grid-list>
      </div>
    </app-responsive-container>

    <!-- Recent Reports Section -->
    <app-responsive-container [isCard]="true">
      <h2>Recent Reports</h2>
      <div class="recent-reports">
        <div class="empty-state">
          <mat-icon class="empty-icon">description</mat-icon>
          <h3>No recent reports</h3>
          <p>Generated reports will appear here for quick access</p>
        </div>
      </div>
    </app-responsive-container>
  }
</app-responsive-container>
