import { Component, signal, computed, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { FormsModule } from '@angular/forms';
import { OrderService, Order } from '../../../core/services/order.service';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { ResponsiveContainerComponent } from '../../../shared/components/responsive-container/responsive-container.component';
import { TableColumn } from '../../../shared/components/responsive-table/responsive-table.component';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-order-list',
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatTableModule,
    FormsModule,
    ResponsiveContainerComponent,
    LoadingComponent
  ],
  templateUrl: './order-list.html',
  styleUrl: './order-list.scss'
})
export class OrderList implements OnInit {
  private orderService = inject(OrderService);
  private responsiveService = inject(ResponsiveService);

  // Signals for state management
  private ordersSignal = signal<Order[]>([]);
  private loadingSignal = signal<boolean>(true);
  private searchQuerySignal = signal<string>('');
  private statusFilterSignal = signal<string>('');

  // Public readonly signals
  readonly orders = this.ordersSignal.asReadonly();
  readonly isLoading = this.loadingSignal.asReadonly();
  readonly searchQuery = this.searchQuerySignal.asReadonly();
  readonly statusFilter = this.statusFilterSignal.asReadonly();

  // Responsive signals
  readonly isMobile = computed(() => this.responsiveService.isMobile());
  readonly containerClasses = computed(() => this.responsiveService.containerClasses());

  // Computed filtered orders
  readonly filteredOrders = computed(() => {
    let filtered = this.orders();

    const query = this.searchQuery().toLowerCase();
    if (query) {
      filtered = filtered.filter(order =>
        order.order_id.toLowerCase().includes(query) ||
        order.customer.name.toLowerCase().includes(query) ||
        order.customer.phone.includes(query)
      );
    }

    const status = this.statusFilter();
    if (status) {
      filtered = filtered.filter(order => order.status === status);
    }

    return filtered;
  });

  // Table columns configuration
  readonly columns: TableColumn[] = [
    { key: 'order_id', label: 'Order ID', sortable: true },
    { key: 'customer', label: 'Customer', sortable: true },
    { key: 'date', label: 'Date', sortable: true, mobileHidden: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'total_amount', label: 'Total Amount', sortable: true, mobileHidden: true },
    { key: 'actions', label: 'Actions', sortable: false }
  ];

  readonly displayedColumns: string[] = ['order_id', 'customer', 'date', 'status', 'total_amount', 'actions'];

  readonly statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'open', label: 'Open' },
    { value: 'closed', label: 'Closed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  ngOnInit(): void {
    this.loadOrders();
  }

  loadOrders(): void {
    this.loadingSignal.set(true);
    this.orderService.getOrders().subscribe({
      next: (orders) => {
        this.ordersSignal.set(orders);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Error loading orders:', error);
        this.loadingSignal.set(false);
      }
    });
  }

  onSearchChange(query: string): void {
    this.searchQuerySignal.set(query);
  }

  onStatusFilterChange(status: string): void {
    this.statusFilterSignal.set(status);
  }

  onRowClicked(order: Order): void {
    // Navigate to order details or edit
    console.log('Order clicked:', order);
  }

  deleteOrder(id: number): void {
    if (confirm('Are you sure you want to delete this order?')) {
      this.orderService.deleteOrder(id).subscribe({
        next: () => {
          this.loadOrders(); // Reload the list
        },
        error: (error) => {
          console.error('Error deleting order:', error);
        }
      });
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'open': return 'primary';
      case 'closed': return 'accent';
      case 'cancelled': return 'warn';
      default: return '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-IN');
  }
}
