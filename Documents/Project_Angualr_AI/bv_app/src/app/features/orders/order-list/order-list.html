<app-responsive-container [class]="containerClasses()" [isCard]="false" [fullHeight]="true">
  <!-- Header -->
  <app-responsive-container [isCard]="true" layout="flex-row" alignment="space-between">
    <h1>Orders Management</h1>
    <button mat-raised-button color="primary" routerLink="/orders/new">
      <mat-icon>add</mat-icon>
      New Order
    </button>
  </app-responsive-container>

  <!-- Filters -->
  <app-responsive-container [isCard]="true" layout="flex-row" alignment="start">
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>Search orders...</mat-label>
      <input matInput
             [value]="searchQuery()"
             (input)="onSearchChange($any($event.target).value)"
             placeholder="Order ID, Customer name, Phone">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline" class="filter-field">
      <mat-label>Status</mat-label>
      <mat-select [value]="statusFilter()" (selectionChange)="onStatusFilterChange($event.value)">
        @for (option of statusOptions; track option.value) {
          <mat-option [value]="option.value">{{ option.label }}</mat-option>
        }
      </mat-select>
    </mat-form-field>

    <button mat-icon-button (click)="loadOrders()" title="Refresh">
      <mat-icon>refresh</mat-icon>
    </button>
  </app-responsive-container>

  <!-- Loading State -->
  @if (isLoading()) {
    <app-loading
      message="Loading orders..."
      [compact]="isMobile()"
      [fullHeight]="false">
    </app-loading>
  } @else {
    <!-- Orders Table -->
    <app-responsive-container [isCard]="true">
      <div class="table-container">
        @if (isMobile()) {
          <!-- Mobile Card Layout -->
          <div class="mobile-cards">
            @for (order of filteredOrders(); track order.id) {
              <mat-card class="order-card" (click)="onRowClicked(order)">
                <mat-card-header>
                  <mat-card-title>{{ order.order_id }}</mat-card-title>
                  <mat-card-subtitle>{{ order.customer.name }}</mat-card-subtitle>
                  <div class="card-actions">
                    <mat-chip [color]="getStatusColor(order.status)" selected>
                      {{ order.status | titlecase }}
                    </mat-chip>
                  </div>
                </mat-card-header>
                <mat-card-content>
                  <div class="order-details">
                    <div class="detail-row">
                      <span class="label">Phone:</span>
                      <span class="value">{{ order.customer.phone }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">Date:</span>
                      <span class="value">{{ formatDate(order.date) }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">Amount:</span>
                      <span class="value">{{ formatCurrency(order.hst_total_order_amt) }}</span>
                    </div>
                  </div>
                </mat-card-content>
                <mat-card-actions>
                  <button mat-icon-button [routerLink]="['/orders', order.id]" title="View">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button [routerLink]="['/orders', order.id, 'edit']" title="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button (click)="deleteOrder(order.id)" color="warn" title="Delete">
                    <mat-icon>delete</mat-icon>
                  </button>
                </mat-card-actions>
              </mat-card>
            }
          </div>
        } @else {
          <!-- Desktop Table Layout -->
          <table mat-table [dataSource]="filteredOrders()" class="orders-table">
            <!-- Order ID Column -->
            <ng-container matColumnDef="order_id">
              <th mat-header-cell *matHeaderCellDef>Order ID</th>
              <td mat-cell *matCellDef="let order">{{ order.order_id }}</td>
            </ng-container>

            <!-- Customer Column -->
            <ng-container matColumnDef="customer">
              <th mat-header-cell *matHeaderCellDef>Customer</th>
              <td mat-cell *matCellDef="let order">
                <div class="customer-info">
                  <div class="customer-name">{{ order.customer.name }}</div>
                  <div class="customer-phone">{{ order.customer.phone }}</div>
                </div>
              </td>
            </ng-container>

            <!-- Date Column -->
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let order">{{ formatDate(order.date) }}</td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let order">
                <mat-chip [color]="getStatusColor(order.status)" selected>
                  {{ order.status | titlecase }}
                </mat-chip>
              </td>
            </ng-container>

            <!-- Total Amount Column -->
            <ng-container matColumnDef="total_amount">
              <th mat-header-cell *matHeaderCellDef>Total Amount</th>
              <td mat-cell *matCellDef="let order">{{ formatCurrency(order.hst_total_order_amt) }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let order">
                <button mat-icon-button [routerLink]="['/orders', order.id]" title="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button [routerLink]="['/orders', order.id, 'edit']" title="Edit">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button (click)="deleteOrder(order.id)" color="warn" title="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="onRowClicked(row)"></tr>
          </table>
        }
      </div>
    </app-responsive-container>

    <!-- Empty State -->
    @if (filteredOrders().length === 0 && !isLoading()) {
      <app-responsive-container [isCard]="true" alignment="center">
        <div class="empty-state">
          <mat-icon class="empty-icon">shopping_cart</mat-icon>
          <h3>No orders found</h3>
          <p>{{ searchQuery() || statusFilter() ? 'Try adjusting your filters' : 'Create your first order to get started' }}</p>
          <button mat-raised-button color="primary" routerLink="/orders/new">
            <mat-icon>add</mat-icon>
            Create Order
          </button>
        </div>
      </app-responsive-container>
    }
  }
</app-responsive-container>
