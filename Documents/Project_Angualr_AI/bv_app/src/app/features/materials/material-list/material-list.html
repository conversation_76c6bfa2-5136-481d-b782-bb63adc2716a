<div class="material-list-container">
  <!-- Header -->
  <div class="page-header">
    <h1>Materials Management</h1>
    <button mat-raised-button color="primary" routerLink="/materials/new">
      <mat-icon>add</mat-icon>
      Add Material
    </button>
  </div>

  <!-- Search and Filters -->
  <mat-card class="search-card">
    <mat-card-content>
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search materials...</mat-label>
        <input matInput
               [value]="searchQuery()"
               (input)="onSearchChange($any($event.target).value)"
               placeholder="Search by name, design, color, or type">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </mat-card-content>
  </mat-card>

  <!-- Materials Table -->
  <mat-card class="table-card">
    @if (isLoading()) {
      <div class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading materials...</p>
      </div>
    } @else {
      <div class="table-container responsive-table">
        <table mat-table [dataSource]="filteredMaterials()" matSort class="materials-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let material">
              <div class="material-name">
                <strong>{{ material.name }}</strong>
              </div>
            </td>
          </ng-container>

          <!-- Design Column -->
          <ng-container matColumnDef="design">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
            <td mat-cell *matCellDef="let material">
              <div class="design-info">
                <span class="design-name">{{ material.design.name }}</span>
                @if (material.design.color && material.design.color !== 'NA') {
                  <span class="design-color">{{ material.design.color }}</span>
                }
              </div>
            </td>
          </ng-container>

          <!-- Sales Type Column -->
          <ng-container matColumnDef="sales_type">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
            <td mat-cell *matCellDef="let material">
              <mat-chip [color]="getSalesTypeColor(material.sales_type)">
                {{ material.sales_type | titlecase }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Available Quantity Column -->
          <ng-container matColumnDef="available_quantity">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Available</th>
            <td mat-cell *matCellDef="let material">
              <div class="quantity-info">
                <span class="quantity">{{ material.available_quantity | number:'1.2-2' }}</span>
                <span class="unit">{{ material.unit.name }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Unit Order Price Column -->
          <ng-container matColumnDef="unit_order_price">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Order Price</th>
            <td mat-cell *matCellDef="let material">
              <span class="price">₹{{ material.unit_order_price | number:'1.2-2' }}</span>
            </td>
          </ng-container>

          <!-- Stock Unit Price Column -->
          <ng-container matColumnDef="stock_unit_price">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Stock Price</th>
            <td mat-cell *matCellDef="let material">
              <span class="price">₹{{ material.stock_unit_price | number:'1.2-2' }}</span>
            </td>
          </ng-container>

          <!-- Total Stock Value Column -->
          <ng-container matColumnDef="total_stock_value">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Stock Value</th>
            <td mat-cell *matCellDef="let material">
              <span class="value">₹{{ material.total_stock_value | number:'1.0-0' }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let material">
              <div class="actions">
                <button mat-icon-button
                        [routerLink]="['/materials', material.id]"
                        title="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button
                        [routerLink]="['/materials', material.id, 'edit']"
                        title="Edit">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button
                        color="warn"
                        (click)="deleteMaterial(material.id)"
                        title="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        @if (filteredMaterials().length === 0) {
          <div class="no-data">
            <mat-icon>inventory_2</mat-icon>
            <h3>No materials found</h3>
            <p>{{ searchQuery() ? 'Try adjusting your search criteria' : 'Start by adding your first material' }}</p>
            @if (!searchQuery()) {
              <button mat-raised-button color="primary" routerLink="/materials/new">
                <mat-icon>add</mat-icon>
                Add Material
              </button>
            }
          </div>
        }
      </div>
    }
  </mat-card>
</div>
