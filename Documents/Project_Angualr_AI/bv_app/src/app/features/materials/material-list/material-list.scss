.material-list-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;

  @media (min-width: 768px) {
    padding: 24px;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  h1 {
    margin: 0;
    color: #333;
    font-size: 24px;
    font-weight: 500;

    @media (max-width: 768px) {
      font-size: 20px;
    }
  }

  button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.search-card {
  margin-bottom: 24px;

  .search-field {
    width: 100%;
    max-width: 400px;
  }
}

.table-card {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;

    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .table-container {
    overflow-x: auto;

    .materials-table {
      width: 100%;
      min-width: 800px;

      th {
        font-weight: 600;
        color: #333;
      }

      td {
        padding: 12px 8px;
      }
    }
  }
}

.material-name {
  strong {
    color: #333;
    font-size: 14px;
  }
}

.design-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .design-name {
    font-weight: 500;
    color: #333;
  }

  .design-color {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
    width: fit-content;
  }
}

.quantity-info {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .quantity {
    font-weight: 500;
    color: #333;
  }

  .unit {
    font-size: 12px;
    color: #666;
  }
}

.price, .value {
  font-weight: 500;
  color: #2e7d32;
}

.actions {
  display: flex;
  gap: 4px;

  button {
    width: 32px;
    height: 32px;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #666;
    font-weight: 500;
  }

  p {
    margin: 0 0 24px 0;
    color: #999;
    font-size: 14px;
  }

  button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;

    button {
      width: 100%;
      justify-content: center;
    }
  }

  .search-card .search-field {
    max-width: none;
  }

  .actions {
    flex-direction: column;

    button {
      width: 100%;
      height: 36px;
    }
  }
}