import { Component, signal, computed, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatChipsModule } from '@angular/material/chips';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MaterialService, Material } from '../../../core/services/material';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { ResponsiveContainerComponent } from '../../../shared/components/responsive-container/responsive-container.component';
import { ResponsiveTableComponent, TableColumn } from '../../../shared/components/responsive-table/responsive-table.component';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-material-list',
  templateUrl: './material-list.html',
  styleUrl: './material-list.scss',
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatSortModule,
    MatChipsModule,
    RouterModule,
    FormsModule
  ]
})
export class MaterialList implements OnInit {
  private materialService = inject(MaterialService);
  private responsiveService = inject(ResponsiveService);

  // Signals for state management
  private materialsSignal = signal<Material[]>([]);
  private loadingSignal = signal<boolean>(true);
  private searchQuerySignal = signal<string>('');

  // Public readonly signals
  materials = this.materialsSignal.asReadonly();
  isLoading = this.loadingSignal.asReadonly();
  searchQuery = this.searchQuerySignal.asReadonly();

  // Responsive signals
  readonly isMobile = computed(() => this.responsiveService.isMobile());
  readonly containerClasses = computed(() => this.responsiveService.containerClasses());
  readonly tableColumns = computed(() => this.responsiveService.tableColumns());

  // Computed filtered materials
  filteredMaterials = computed(() => {
    const query = this.searchQuery().toLowerCase();
    if (!query) return this.materials();

    return this.materials().filter(material =>
      material.name.toLowerCase().includes(query) ||
      material.design.name.toLowerCase().includes(query) ||
      material.design.color.toLowerCase().includes(query) ||
      material.type.name.toLowerCase().includes(query)
    );
  });

  displayedColumns: string[] = [
    'name',
    'design',
    'sales_type',
    'available_quantity',
    'unit_order_price',
    'stock_unit_price',
    'total_stock_value',
    'actions'
  ];

  // Table columns configuration for responsive table
  readonly columns: TableColumn[] = [
    { key: 'name', label: 'Material Name', sortable: true },
    { key: 'design', label: 'Design', sortable: true, mobileHidden: false },
    { key: 'sales_type', label: 'Sales Type', sortable: true, mobileHidden: true },
    { key: 'available_quantity', label: 'Quantity', sortable: true },
    { key: 'unit_order_price', label: 'Unit Price', sortable: true, mobileHidden: true },
    { key: 'stock_unit_price', label: 'Stock Price', sortable: true, mobileHidden: true, tabletHidden: true },
    { key: 'total_stock_value', label: 'Total Value', sortable: true, mobileHidden: true },
    { key: 'actions', label: 'Actions', sortable: false }
  ];

  ngOnInit(): void {
    this.loadMaterials();
  }

  loadMaterials(): void {
    this.loadingSignal.set(true);
    this.materialService.getMaterials().subscribe({
      next: (materials) => {
        this.materialsSignal.set(materials);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Error loading materials:', error);
        this.loadingSignal.set(false);
      }
    });
  }

  onSearchChange(query: string): void {
    this.searchQuerySignal.set(query);
  }

  deleteMaterial(id: number): void {
    if (confirm('Are you sure you want to delete this material?')) {
      this.materialService.deleteMaterial(id).subscribe({
        next: () => {
          this.loadMaterials(); // Reload the list
        },
        error: (error) => {
          console.error('Error deleting material:', error);
        }
      });
    }
  }

  getSalesTypeColor(salesType: string): string {
    return salesType === 'single' ? 'primary' : 'accent';
  }
}
