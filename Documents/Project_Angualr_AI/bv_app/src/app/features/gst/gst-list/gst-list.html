<app-responsive-container [class]="containerClasses()" [isCard]="false" [fullHeight]="true">
  <!-- Header -->
  <app-responsive-container [isCard]="true" layout="flex-row" alignment="space-between">
    <h1>GST Management</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary">
        <mat-icon>add</mat-icon>
        Add GST Material
      </button>
      <button mat-raised-button color="accent">
        <mat-icon>add</mat-icon>
        Add Tax Rate
      </button>
    </div>
  </app-responsive-container>

  <!-- Loading State -->
  @if (isLoading()) {
    <app-loading
      message="Loading GST data..."
      [compact]="isMobile()"
      [fullHeight]="false">
    </app-loading>
  } @else {
    <!-- Tabs for different GST sections -->
    <app-responsive-container [isCard]="true">
      <mat-tab-group (selectedTabChange)="onTabChange($event.index)">
        <!-- GST Materials Tab -->
        <mat-tab label="GST Materials">
          <div class="tab-content">
            @if (gstMaterials().length === 0) {
              <div class="empty-state">
                <mat-icon class="empty-icon">receipt</mat-icon>
                <h3>No GST materials found</h3>
                <p>Add GST information for your materials to enable GST billing</p>
                <button mat-raised-button color="primary">
                  <mat-icon>add</mat-icon>
                  Add GST Material
                </button>
              </div>
            } @else {
              <table mat-table [dataSource]="gstMaterials()" class="gst-table">
                <!-- Material Name Column -->
                <ng-container matColumnDef="material_name">
                  <th mat-header-cell *matHeaderCellDef>Material</th>
                  <td mat-cell *matCellDef="let material">
                    <div class="material-info">
                      <div class="material-name">{{ material.material.name }}</div>
                      <div class="material-design">{{ material.material.design.name }} - {{ material.material.design.color }}</div>
                    </div>
                  </td>
                </ng-container>

                <!-- HSN Code Column -->
                <ng-container matColumnDef="hsn_code">
                  <th mat-header-cell *matHeaderCellDef>HSN Code</th>
                  <td mat-cell *matCellDef="let material">{{ material.hsn_code }}</td>
                </ng-container>

                <!-- GST Rate Column -->
                <ng-container matColumnDef="gst_rate">
                  <th mat-header-cell *matHeaderCellDef>GST Rate</th>
                  <td mat-cell *matCellDef="let material">
                    <mat-chip color="primary" selected>{{ material.gst_rate }}%</mat-chip>
                  </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let material">
                    <button mat-icon-button title="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="deleteGstMaterial(material.id)" color="warn" title="Delete">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="materialColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: materialColumns;"></tr>
              </table>
            }
          </div>
        </mat-tab>

        <!-- GST Tax Rates Tab -->
        <mat-tab label="Tax Rates">
          <div class="tab-content">
            @if (gstTaxes().length === 0) {
              <div class="empty-state">
                <mat-icon class="empty-icon">percent</mat-icon>
                <h3>No tax rates found</h3>
                <p>Configure GST tax rates for your business</p>
                <button mat-raised-button color="primary">
                  <mat-icon>add</mat-icon>
                  Add Tax Rate
                </button>
              </div>
            } @else {
              <table mat-table [dataSource]="gstTaxes()" class="gst-table">
                <!-- Name Column -->
                <ng-container matColumnDef="name">
                  <th mat-header-cell *matHeaderCellDef>Name</th>
                  <td mat-cell *matCellDef="let tax">{{ tax.name }}</td>
                </ng-container>

                <!-- Rate Column -->
                <ng-container matColumnDef="rate">
                  <th mat-header-cell *matHeaderCellDef>Rate</th>
                  <td mat-cell *matCellDef="let tax">
                    <mat-chip color="accent" selected>{{ tax.rate }}%</mat-chip>
                  </td>
                </ng-container>

                <!-- Description Column -->
                <ng-container matColumnDef="description">
                  <th mat-header-cell *matHeaderCellDef>Description</th>
                  <td mat-cell *matCellDef="let tax">{{ tax.description }}</td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="is_active">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let tax">
                    <mat-chip [color]="tax.is_active ? 'primary' : 'warn'" selected>
                      {{ tax.is_active ? 'Active' : 'Inactive' }}
                    </mat-chip>
                  </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let tax">
                    <button mat-icon-button title="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="deleteGstTax(tax.id)" color="warn" title="Delete">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="taxColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: taxColumns;"></tr>
              </table>
            }
          </div>
        </mat-tab>

        <!-- GST Orders Tab -->
        <mat-tab label="GST Orders">
          <div class="tab-content">
            @if (gstOrders().length === 0) {
              <div class="empty-state">
                <mat-icon class="empty-icon">shopping_cart</mat-icon>
                <h3>No GST orders found</h3>
                <p>GST orders will appear here when created</p>
              </div>
            } @else {
              <table mat-table [dataSource]="gstOrders()" class="gst-table">
                <!-- Order ID Column -->
                <ng-container matColumnDef="order_id">
                  <th mat-header-cell *matHeaderCellDef>Order ID</th>
                  <td mat-cell *matCellDef="let order">{{ order.order_id }}</td>
                </ng-container>

                <!-- Customer Column -->
                <ng-container matColumnDef="customer">
                  <th mat-header-cell *matHeaderCellDef>Customer</th>
                  <td mat-cell *matCellDef="let order">
                    <div class="customer-info">
                      <div class="customer-name">{{ order.customer.name }}</div>
                      <div class="customer-gst">{{ order.customer.gst_number || 'No GST' }}</div>
                    </div>
                  </td>
                </ng-container>

                <!-- Date Column -->
                <ng-container matColumnDef="date">
                  <th mat-header-cell *matHeaderCellDef>Date</th>
                  <td mat-cell *matCellDef="let order">{{ formatDate(order.date) }}</td>
                </ng-container>

                <!-- Total Amount Column -->
                <ng-container matColumnDef="total_amount">
                  <th mat-header-cell *matHeaderCellDef>Total Amount</th>
                  <td mat-cell *matCellDef="let order">{{ formatCurrency(order.total_amount) }}</td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let order">
                    <mat-chip [color]="getStatusColor(order.status)" selected>
                      {{ order.status | titlecase }}
                    </mat-chip>
                  </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let order">
                    <button mat-icon-button title="View">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button title="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="deleteGstOrder(order.id)" color="warn" title="Delete">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="orderColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: orderColumns;"></tr>
              </table>
            }
          </div>
        </mat-tab>
      </mat-tab-group>
    </app-responsive-container>
  }
</app-responsive-container>
