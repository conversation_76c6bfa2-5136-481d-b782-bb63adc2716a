import { Component, signal, computed, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { GstService, GstMaterial, GstTax, GstOrder } from '../../../core/services/gst.service';
import { ResponsiveService } from '../../../core/services/responsive.service';
import { ResponsiveContainerComponent } from '../../../shared/components/responsive-container/responsive-container.component';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-gst-list',
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    ResponsiveContainerComponent,
    LoadingComponent
  ],
  templateUrl: './gst-list.html',
  styleUrl: './gst-list.scss'
})
export class GstList implements OnInit {
  private gstService = inject(GstService);
  private responsiveService = inject(ResponsiveService);

  // Signals for state management
  private gstMaterialsSignal = signal<GstMaterial[]>([]);
  private gstTaxesSignal = signal<GstTax[]>([]);
  private gstOrdersSignal = signal<GstOrder[]>([]);
  private loadingSignal = signal<boolean>(true);
  private selectedTabSignal = signal<number>(0);

  // Public readonly signals
  readonly gstMaterials = this.gstMaterialsSignal.asReadonly();
  readonly gstTaxes = this.gstTaxesSignal.asReadonly();
  readonly gstOrders = this.gstOrdersSignal.asReadonly();
  readonly isLoading = this.loadingSignal.asReadonly();
  readonly selectedTab = this.selectedTabSignal.asReadonly();

  // Responsive signals
  readonly isMobile = computed(() => this.responsiveService.isMobile());
  readonly containerClasses = computed(() => this.responsiveService.containerClasses());

  // Table columns
  readonly materialColumns = ['material_name', 'hsn_code', 'gst_rate', 'actions'];
  readonly taxColumns = ['name', 'rate', 'description', 'is_active', 'actions'];
  readonly orderColumns = ['order_id', 'customer', 'date', 'total_amount', 'status', 'actions'];

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loadingSignal.set(true);

    // Load all GST data
    Promise.all([
      this.gstService.getGstMaterials().toPromise(),
      this.gstService.getGstTaxes().toPromise(),
      this.gstService.getGstOrders().toPromise()
    ]).then(([materials, taxes, orders]) => {
      this.gstMaterialsSignal.set(materials || []);
      this.gstTaxesSignal.set(taxes || []);
      this.gstOrdersSignal.set(orders || []);
      this.loadingSignal.set(false);
    }).catch(error => {
      console.error('Error loading GST data:', error);
      this.loadingSignal.set(false);
    });
  }

  onTabChange(index: number): void {
    this.selectedTabSignal.set(index);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-IN');
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'open': return 'primary';
      case 'closed': return 'accent';
      case 'cancelled': return 'warn';
      default: return '';
    }
  }

  deleteGstMaterial(id: number): void {
    if (confirm('Are you sure you want to delete this GST material?')) {
      this.gstService.deleteGstMaterial(id).subscribe({
        next: () => this.loadData(),
        error: (error) => console.error('Error deleting GST material:', error)
      });
    }
  }

  deleteGstTax(id: number): void {
    if (confirm('Are you sure you want to delete this GST tax?')) {
      this.gstService.deleteGstTax(id).subscribe({
        next: () => this.loadData(),
        error: (error) => console.error('Error deleting GST tax:', error)
      });
    }
  }

  deleteGstOrder(id: number): void {
    if (confirm('Are you sure you want to delete this GST order?')) {
      this.gstService.deleteGstOrder(id).subscribe({
        next: () => this.loadData(),
        error: (error) => console.error('Error deleting GST order:', error)
      });
    }
  }
}
