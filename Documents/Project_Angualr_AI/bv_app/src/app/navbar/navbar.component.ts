import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';
import { AuthService } from '../core/auth/auth';

@Component({
  selector: 'app-navbar',
  standalone: true,
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
  imports: [CommonModule, MatToolbarModule, MatButtonModule]
})
export class NavbarComponent {
  private auth = inject(AuthService);
  private router = inject(Router);
  user = this.auth.user;

  logout() {
    this.auth.logout();
  }
}
