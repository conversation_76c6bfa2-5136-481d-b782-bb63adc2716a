import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, throwError } from 'rxjs';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);

  // Add credentials to all requests to include session cookies
  // Only set Content-Type for non-GET requests
  const headers: { [key: string]: string } = {};
  if (req.method !== 'GET' && !req.headers.has('Content-Type')) {
    headers['Content-Type'] = 'application/json';
  }

  const authReq = req.clone({
    setHeaders: headers,
    withCredentials: true
  });

  return next(authReq).pipe(
    catchError(error => {
      // Handle authentication errors
      if (error.status === 401 || error.status === 403) {
        // Only redirect to login if not already on login page
        if (!router.url.includes('/login')) {
          router.navigate(['/login']);
        }
      }

      return throwError(() => error);
    })
  );
};
