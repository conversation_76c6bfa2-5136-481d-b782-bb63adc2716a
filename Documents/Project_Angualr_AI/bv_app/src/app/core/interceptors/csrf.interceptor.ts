import { HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { switchMap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { environment } from '../../../environments/environment';

export const csrfInterceptor: HttpInterceptorFn = (req, next) => {
  // Skip CSRF token for GET requests, external URLs, CSRF endpoint, and login endpoint
  if (req.method === 'GET' ||
      !req.url.includes(environment.apiBaseUrl) ||
      req.url.includes('/auth/csrf/') ||
      req.url.includes('/auth/login/')) {
    return next(req);
  }

  // Check if CSRF token is already present
  if (req.headers.has('X-CSRFToken')) {
    return next(req);
  }

  // Try to get CSRF token from cookie first
  const csrfTokenFromCookie = getCsrfTokenFromCookie();
  if (csrfTokenFromCookie) {
    const csrfReq = req.clone({
      setHeaders: {
        'X-CSRFToken': csrfTokenFromCookie
      },
      withCredentials: true
    });
    return next(csrfReq);
  }

  // For non-login requests, continue without CSRF token
  // (since login is exempted, other endpoints might need different handling)
  const credentialsReq = req.clone({
    withCredentials: true
  });
  return next(credentialsReq);
};

function getCsrfTokenFromCookie(): string | null {
  const name = 'csrftoken';
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}
