import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { Auth } from '../auth/auth';

export const authGuard: CanActivateFn = (route, state) => {
  const auth = inject(Auth);
  const router = inject(Router);

  console.log('🛡️ Auth Guard - isAuthenticated:', auth.isAuthenticated());
  console.log('🛡️ Auth Guard - user:', auth.user());
  console.log('🛡️ Auth Guard - route:', state.url);

  if (auth.isAuthenticated()) {
    console.log('✅ Auth Guard - Access granted');
    return true;
  } else {
    console.log('❌ Auth Guard - Access denied, redirecting to login');
    router.navigate(['/login']);
    return false;
  }
};

export const loginGuard: CanActivateFn = (route, state) => {
  const auth = inject(Auth);
  const router = inject(Router);

  console.log('🔐 Login Guard - isAuthenticated:', auth.isAuthenticated());
  console.log('🔐 Login Guard - user:', auth.user());
  console.log('🔐 Login Guard - route:', state.url);

  if (auth.isAuthenticated()) {
    console.log('✅ Login Guard - User authenticated, redirecting to dashboard');
    router.navigate(['/dashboard']);
    return false;
  } else {
    console.log('✅ Login Guard - User not authenticated, allowing access to login');
    return true;
  }
};
