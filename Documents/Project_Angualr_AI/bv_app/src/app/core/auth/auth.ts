import { Injectable, signal, computed, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

export interface User {
  id: number;
  username: string;
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  private http = inject(HttpClient);
  private router = inject(Router);
  private userSignal = signal<User | null>(null);
  private loadingSignal = signal(false);
  private errorSignal = signal<string | null>(null);

  readonly user = this.userSignal.asReadonly();
  readonly isAuthenticated = computed(() => this.userSignal() !== null);
  readonly isLoading = this.loadingSignal.asReadonly();
  readonly error = this.errorSignal.asReadonly();

  login(username: string, password: string) {
    this.loadingSignal.set(true);
    this.http.post<User>(`${environment.apiUrl}/auth/login/`, { username, password }, { withCredentials: true })
      .subscribe({
        next: user => {
          this.userSignal.set(user);
          this.loadingSignal.set(false);
          this.router.navigate(['/dashboard']);
        },
        error: err => {
          this.errorSignal.set('Login failed');
          this.loadingSignal.set(false);
        }
      });
  }

  logout() {
    this.http.post(`${environment.apiUrl}/auth/logout/`, {}, { withCredentials: true }).subscribe({
      complete: () => {
        this.userSignal.set(null);
        this.router.navigate(['/login']);
      }
    });
  }

  checkAuth() {
    this.loadingSignal.set(true);
    this.http.get<User>(`${environment.apiUrl}/auth/user/`, { withCredentials: true })
      .subscribe({
        next: user => {
          this.userSignal.set(user);
          this.loadingSignal.set(false);
        },
        error: () => {
          this.userSignal.set(null);
          this.loadingSignal.set(false);
        }
      });
  }
}
